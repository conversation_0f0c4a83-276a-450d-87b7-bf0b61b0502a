part of xr_helper;

class GetStorageService {
  static const String _boxName = 'calendar_app_storage';

  static final _box = GetStorage(_boxName);

  static Future<void> init() async {
    await GetStorage.init();
    await GetStorage().erase();
    await GetStorage.init(_boxName);
  }

  static Future<bool> setData(
      {required String key, required dynamic value}) async {
    try {
      await _box.write(key, value);

      Log.i("Local data saved successfully");
      return true;
    } on Exception catch (e) {
      Log.e("Error while saving local data $e");
      return false;
    }
  }

  static dynamic getData({required dynamic key}) {
    return _box.read(key);
  }

  Future<bool> removeLocalData({required String key}) async {
    try {
      await _box.remove(key);

      Log.i("Local data removed successfully");
      return true;
    } on Exception catch (e) {
      Log.e("Error while removing local data $e");
      return false;
    }
  }

  static bool hasData({required String key}) {
    return _box.hasData(key);
  }

  static Future<bool> clearLocalData() async {
    try {
      await _box.erase();

      Log.i("Local data cleared successfully");
      return true;
    } on Exception catch (e) {
      Log.e("Error while clearing local data $e");
      return false;
    }
  }

  //? Remove Key
  static Future<bool> removeKey({required String key}) async {
    try {
      await _box.remove(key);

      Log.i("Local data removed successfully");
      return true;
    } on Exception catch (e) {
      Log.e("Error while removing local data $e");
      return false;
    }
  }
}

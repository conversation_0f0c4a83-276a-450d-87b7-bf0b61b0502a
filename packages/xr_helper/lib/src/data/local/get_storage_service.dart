part of xr_helper;

class GetStorageService {
  static const String _boxName = 'calendar_app_storage';

  static final _box = GetStorage(_boxName);

  static Future<void> init() async {
    await GetStorage.init();
    await GetStorage().erase();
    await GetStorage.init(_boxName);

    // Clean up old data periodically to prevent storage accumulation
    await _cleanupOldData();
  }

  /// Clean up old data to prevent storage accumulation
  static Future<void> _cleanupOldData() async {
    try {
      final box = GetStorage(_boxName);
      final keys = box.getKeys();

      // Remove temporary or old keys that might accumulate
      final keysToRemove = keys.where((key) =>
        key.toString().contains('temp_') ||
        key.toString().contains('old_') ||
        key.toString().contains('cache_')
      ).toList();

      for (final key in keysToRemove) {
        try {
          await box.remove(key);
        } catch (e) {
          // Ignore individual key removal errors
        }
      }
    } catch (e) {
      // Ignore cleanup errors to not affect main functionality
    }
  }

  static Future<bool> setData(
      {required String key, required dynamic value}) async {
    try {
      await _box.write(key, value);

      // Data saved - minimal logging to reduce storage usage
      return true;
    } on Exception catch (e) {
      Log.e("Error while saving local data $e");
      return false;
    }
  }

  static dynamic getData({required dynamic key}) {
    return _box.read(key);
  }

  Future<bool> removeLocalData({required String key}) async {
    try {
      await _box.remove(key);

      // Data removed - minimal logging
      return true;
    } on Exception catch (e) {
      Log.e("Error while removing local data $e");
      return false;
    }
  }

  static bool hasData({required String key}) {
    return _box.hasData(key);
  }

  static Future<bool> clearLocalData() async {
    try {
      await _box.erase();

      // Data cleared - minimal logging
      return true;
    } on Exception catch (e) {
      Log.e("Error while clearing local data $e");
      return false;
    }
  }

  //? Remove Key
  static Future<bool> removeKey({required String key}) async {
    try {
      await _box.remove(key);

      // Data removed - minimal logging
      return true;
    } on Exception catch (e) {
      Log.e("Error while removing local data $e");
      return false;
    }
  }
}

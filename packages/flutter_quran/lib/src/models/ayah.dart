import 'package:flutter_quran/src/utils/string_extensions.dart';

class Ayah {
  final int id,
      jozz,
      surahNumber,
      page,
      lineStart,
      lineEnd,
      ayahNumber,
      quarter,
      hizb;
  final String surahNameEn, surahNameAr, ayahText;
  String ayah;
  final bool sajda;
  bool centered;

  Ayah({
    required this.id,
    required this.jozz,
    required this.surahNumber,
    required this.page,
    required this.lineStart,
    required this.lineEnd,
    required this.ayahNumber,
    required this.quarter,
    required this.hizb,
    required this.surahNameEn,
    required this.surahNameAr,
    required this.ayah,
    required this.ayahText,
    required this.sajda,
    required this.centered,
  });

  Ayah.fromSearch({
    required this.id,
    required this.page,
    this.jozz = 0,
    this.lineStart = 0,
    this.surahNumber = 0,
    this.lineEnd = 0,
    this.ayahNumber = 0,
    this.quarter = -1,
    this.hizb = -1,
    this.surahNameEn = '',
    this.surahNameAr = '',
    this.ayah = '',
    this.ayahText = '',
    this.sajda = false,
    this.centered = false,
  });

  Map<String, dynamic> toJson() => {
        'id': id,
        'jozz': jozz,
        'sora': surahNumber,
        'page': page,
        'line_start': lineStart,
        'line_end': lineEnd,
        'aya_no': ayahNumber,
        'sora_name_en': surahNameEn,
        'sora_name_ar': surahNameAr,
        'aya_text': ayah,
        'aya_text_emlaey': ayahText,
        'centered': centered,
      };

  @override
  String toString() =>
      "\"id\": $id, \"jozz\": $jozz,\"sora\": $surahNumber,\"page\": $page,\"line_start\": $lineStart,\"line_end\": $lineEnd,\"aya_no\": $ayahNumber,\"sora_name_en\": \"$surahNameEn\",\"sora_name_ar\": \"$surahNameAr\",\"aya_text\": \"${ayah.replaceAll("\n", "\\n")}\",\"aya_text_emlaey\": \"${ayahText.replaceAll("\n", "\\n")}\",\"centered\": $centered";

  factory Ayah.fromJson(Map<String, dynamic> json) {
    String ayahText = json['aya_text'];
    if (ayahText[ayahText.length - 1] == '\n') {
      ayahText = ayahText.insert(' ', ayahText.length - 1);
    } else {
      ayahText = '$ayahText ';
    }
    return Ayah(
      id: json['id'],
      jozz: json['jozz'],
      surahNumber: json['sora'] ?? 0,
      page: json['page'],
      lineStart: json['line_start'],
      lineEnd: json['line_end'],
      ayahNumber: json['aya_no'],
      quarter: -1,
      hizb: -1,
      surahNameEn: json['sora_name_en'],
      surahNameAr: json['sora_name_ar'],
      ayah: ayahText,
      ayahText: json['aya_text_emlaey'],
      sajda: false,
      centered: json['centered'] ?? false,
    );
  }

  factory Ayah.fromAya({
    required Ayah ayah,
    required String aya,
    required String ayaText,
    bool centered = false,
  }) =>
      Ayah(
        id: ayah.id,
        jozz: ayah.jozz,
        surahNumber: ayah.surahNumber,
        page: ayah.page,
        lineStart: ayah.lineStart,
        lineEnd: ayah.lineEnd,
        ayahNumber: ayah.ayahNumber,
        quarter: ayah.quarter,
        hizb: ayah.hizb,
        surahNameEn: ayah.surahNameEn,
        surahNameAr: ayah.surahNameAr,
        ayah: aya,
        ayahText: ayaText,
        sajda: false,
        centered: centered,
      );
}

import 'package:flutter/material.dart';

class QuranColorManager {
  static const primaryColor = Color(0xFF007522);

  static const quranBackgroundColor = Color(0xffF1EEE5);
  // static const primaryColor = Color(0xFF005e31);
  static final lightPrimaryColor = const Color(0xFF007522).withOpacity(0.1);
  static final lightPrimaryColor2 = const Color(0xFF007522).withOpacity(0.15);
  static final lightPrimaryColor3 = const Color(0xFF007522).withOpacity(0.45);

  // static const secondaryColor =
  // Color(0xFFB8860B);
  static const secondaryColor = Color(0xFFffbf00);
  static final lightSecondaryColor = const Color(0xFFffbf00).withOpacity(0.1);
  static final lightSecondaryColor2 = const Color(0xFFffbf00).withOpacity(0.6);
  // Colors.amber.shade600;
  static const lightGrey2 = Color(0xFFECF0FF);

  static const buttonColor = lightGrey2;
  static final containerColor = Colors.grey.withOpacity(0.1);
  static const selectedContainerColor = Color(0xFFD3E1E2);
  static const fieldColor = Color(0xFFCBD5E1);
  static const white = Color(0xFFFFFFFF);
  static const black = Color(0xFF000000);
  static const grey = Color(0xFFf5f5f5);
  static const greyIcon = Color(0xFF9E9E9E);
  static const highlightColor = Color(0xFFFFFFFF);
  static const lightGrey = Color(0xFFEEF1F6);

  static const shimmerBaseColor = Color(0xFFCECECE);
  static const cardColor = Color(0xFFEDEDED);
  static const darkGrey = Color(0xFFA4A4A4);
  static const blueGrey = Color(0xFFd8dfe3);
  static const darkBlue = Color(0xFF23292F);
  static const iconColor = Color(0xFF727272);
  static const errorColor = Color(0xFFE74C3C);
  static const successColor = Color(0xFF2ECC71);
}

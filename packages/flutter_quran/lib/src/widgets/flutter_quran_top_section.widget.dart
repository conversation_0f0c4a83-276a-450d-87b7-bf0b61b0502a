import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_quran/flutter_quran.dart';
import 'package:flutter_quran/src/utils/quran_color_manager.dart';
import 'package:quran/quran.dart';
import 'package:xr_helper/xr_helper.dart';

class FlutterQuranTopSectionWidget extends HookWidget {
  final ValueNotifier<int?> currentIndex;
  final int? page;
  final String surah;
  final int? hizb;

  const FlutterQuranTopSectionWidget({
    super.key,
    required this.currentIndex,
    required this.page,
    required this.hizb,
    required this.surah,
  });

  @override
  Widget build(BuildContext context) {
    final isPageFavourite = useBookmark(page ?? currentIndex.value ?? 1);

    // var hizbText = hizb == null
    //     ? ''
    //     : '${mapNumberToHizbPart(hizb!)} الحزب ${QuranConstants.quranHizbs[(hizb! / 4).floor()]}';

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        const Spacer(
          flex: 3,
        ),
        Padding(
          padding: const EdgeInsets.only(left: 0.0),
          child: ValueListenableBuilder<int?>(
            valueListenable: currentIndex,
            builder: (context, value, child) {
              if (value == null) {
                return const SizedBox.shrink();
              }
              return Text(
                "الجزء ${getJuzNumber(getPageData(value)[0]["surah"], getPageData(value)[0]["start"])}",
                style: FlutterQuran().hafsStyle.copyWith(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                // AppTextStyles.labelLarge.copyWith(
                //   fontSize: 13,
                //   color: Colors.black,
                // ),
              );
            },
          ),
        ),
        const Spacer(
          flex: 2,
        ),
        GestureDetector(
          onTap: () {
            if (isPageFavourite.value) {
              GetStorageService.removeKey(key: LocalKeys.favoritePage);
            } else {
              GetStorageService.setData(
                key: LocalKeys.favoritePage,
                value: currentIndex.value,
              );
            }
          },
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: isPageFavourite.value
                  ? QuranColorManager.primaryColor
                  : QuranColorManager.secondaryColor,
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              isPageFavourite.value
                  ? CupertinoIcons.bookmark_fill
                  : CupertinoIcons.bookmark,
              color: QuranColorManager.white,
              size: 16,
            ),
          ),
        ),
        const Spacer(
          flex: 2,
        ),
        Padding(
          padding: const EdgeInsets.only(right: 0),
          child: Text(
            surah,
            style: FlutterQuran().hafsStyle.copyWith(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
          ),
        ),
        const Spacer(
          flex: 3,
        ),
      ],
    );
  }

  String mapNumberToHizbPart(int number) {
    final reminder = (number / 4) % 1;
    if (number / 4 == 0) {
      return '';
    } else if (reminder == 0.25) {
      return 'ربع';
    } else if (reminder == 0.5) {
      return 'نصف';
    } else if (reminder == 0.75) {
      return 'ثلاثة أرباع';
    } else {
      return '';
    }
  }
}

//import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_hooks/flutter_hooks.dart';
// import 'package:flutter_quran/flutter_quran.dart';
// import 'package:flutter_quran/src/models/quran_constants.dart';
// import 'package:flutter_quran/src/utils/quran_color_manager.dart';
// import 'package:quran/quran.dart';
// import 'package:xr_helper/xr_helper.dart';
//
// class TopSectionWidget extends HookWidget {
//   final ValueNotifier<int?> currentIndex;
//   final int? page;
//   final String surah;
//   final int? hizb;
//
//   const TopSectionWidget({
//     super.key,
//     required this.currentIndex,
//     required this.page,
//     required this.hizb,
//     required this.surah,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     final isPageFavourite = useBookmark(page ?? currentIndex.value ?? 1);
//
//     var hizbText = hizb == null
//         ? ''
//         : '${mapNumberToHizbPart(hizb!)} الحزب ${QuranConstants.quranHizbs[(hizb! / 4).floor()]}';
//
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//       children: [
//         Row(
//           children: [
//             const Icon(
//               CupertinoIcons.book,
//               color: QuranColorManager.primaryColor,
//             ),
//             AppGaps.gap8,
//             Text(
//               surah,
//               style: FlutterQuran().hafsStyle.copyWith(
//                     fontSize: 18,
//                     fontWeight: FontWeight.bold,
//                     color: Colors.black,
//                   ),
//             ),
//           ],
//         ),
//         if (hizbText.isNotEmpty)
//           Text(
//             hizbText,
//             style: AppTextStyles.labelLarge.copyWith(
//               fontSize: 13,
//               color: Colors.black.withOpacity(0.6),
//             ),
//           ),
//         Row(
//           children: [
//             ValueListenableBuilder<int?>(
//               valueListenable: currentIndex,
//               builder: (context, value, child) {
//                 if (value == null) {
//                   return const SizedBox.shrink();
//                 }
//                 return Text(
//                   "الجزء ${getJuzNumber(getPageData(value)[0]["surah"], getPageData(value)[0]["start"])}",
//                   style: AppTextStyles.labelLarge.copyWith(
//                     fontSize: 13,
//                     color: Colors.black,
//                   ),
//                 );
//               },
//             ),
//             AppGaps.gap8,
//             CircleAvatar(
//               backgroundColor: isPageFavourite.value
//                   ? QuranColorManager.primaryColor
//                   : QuranColorManager.secondaryColor,
//               radius: 20,
//               child: IconButton(
//                 icon: Icon(
//                   isPageFavourite.value
//                       ? CupertinoIcons.bookmark_fill
//                       : CupertinoIcons.bookmark,
//                   color: QuranColorManager.white,
//                   size: 18,
//                 ),
//                 onPressed: () {
//                   // GetStorageService.setData(
//                   //   key: LocalKeys.favoritePage,
//                   //   value: currentIndex.value,
//                   // );
//                   //
//                   // isPageFavourite.value = !isPageFavourite.value;
//
//                   if (isPageFavourite.value) {
//                     GetStorageService.removeKey(key: LocalKeys.favoritePage);
//                   } else {
//                     GetStorageService.setData(
//                       key: LocalKeys.favoritePage,
//                       value: currentIndex.value,
//                     );
//                   }
//                 },
//               ),
//             ),
//           ],
//         ),
//       ],
//     );
//   }
//
//   String mapNumberToHizbPart(int number) {
//     final reminder = (number / 4) % 1;
//     if (number / 4 == 0) {
//       return '';
//     } else if (reminder == 0.25) {
//       return 'ربع';
//     } else if (reminder == 0.5) {
//       return 'نصف';
//     } else if (reminder == 0.75) {
//       return 'ثلاثة أرباع';
//     } else {
//       return '';
//     }
//   }
// }

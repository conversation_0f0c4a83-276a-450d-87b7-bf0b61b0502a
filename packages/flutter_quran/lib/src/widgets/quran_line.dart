part of '../flutter_quran_screen.dart';

OverlayEntry? currentAyahPopup;

class QuranLine extends HookWidget {
  const QuranLine(
    this.line,
    this.bookmarksAyahs,
    this.bookmarks, {
    super.key,
    this.boxFit = BoxFit.fill,
    this.onLongPress,
    required this.selectedSpan,
    required this.audioPlayer,
    this.highlightVerseNumber,
    this.highlightVerseSurahNumber,
    required this.currentPage,
  });

  final Line line;
  final List<int> bookmarksAyahs;
  final List<Bookmark> bookmarks;
  final BoxFit boxFit;
  final int currentPage;
  final Function? onLongPress;
  final ValueNotifier<String> selectedSpan;
  final ValueNotifier<AudioPlayer> audioPlayer;
  final int? highlightVerseNumber;
  final int? highlightVerseSurahNumber;

  @override
  Widget build(BuildContext context) {
    // final audioPlayer = useState(AudioPlayer());

    return FittedBox(
        fit: boxFit,
        child: RichText(
            text: TextSpan(
          children: line.ayahs.reversed.map((ayah) {
            final surah = ayah.surahNumber;
            final verse = ayah.ayahNumber;

            return WidgetSpan(
              child: HookBuilder(builder: (context) {
                final isBookmarked = useState(GetStorageService.getData(
                          key: LocalKeys.favoriteVersePage,
                        ) ==
                        currentPage &&
                    GetStorageService.getData(
                          key: LocalKeys.favoriteVerse,
                        ) ==
                        ayah.id);

                void _showPopupMenu(
                  Offset position,
                ) {
                  final overlay = Overlay.of(context);

                  currentAyahPopup?.remove();
                  currentAyahPopup = null;
                  selectedSpan.value = "";

                  currentAyahPopup = OverlayEntry(
                    builder: (context) => Positioned(
                      left: position.dx - 50,
                      top: position.dy - 100,
                      child: HookBuilder(builder: (context) {
                        final isPlaying = useState(false);

                        Future<void> stopAudio() async {
                          try {
                            await audioPlayer.value.stop();
                            isPlaying.value = false;
                          } catch (e) {
                            debugPrint('Error stopping audio: $e');
                          }
                        }

                        Future<void> startAudio() async {
                          try {
                            // await audioPlayer.value.stop();
                            final hasInternet =
                                await InternetConnection().hasInternetAccess;

                            Log.w('Internet_Connection: $hasInternet');

                            if (!hasInternet) {
                              Fluttertoast.showToast(
                                msg: 'لا يوجد اتصال بالإنترنت',
                                backgroundColor: QuranColorManager.errorColor,
                              );

                              return;
                            }

                            isPlaying.value = true;
                            await audioPlayer.value.setUrl(
                                getAudioURLByVerse(surah, verse, "ar.minshawi"),
                                tag: MediaItem(
                                  id: '${getAudioURLByVerse(surah, verse, "ar.minshawi").hashCode}',
                                  title:
                                      "سورة ${getSurahName(surah)} - آية $verse",
                                  album: "القرآن الكريم",
                                ));

                            await audioPlayer.value.play();

                            audioPlayer.value.playerStateStream.listen((event) {
                              if (event.processingState ==
                                  ProcessingState.completed) {
                                isPlaying.value = false;
                              }
                            });
                          } catch (e) {
                            debugPrint('Error starting audio: $e');
                          }
                        }

                        // void share() {
                        //   Share.share(
                        //     'سورة ${getSurahName(surah)} - آية $verse\n\n${ayah.ayahText}',
                        //   );
                        // }

                        return Material(
                          color: Colors.transparent,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(8.0),
                              boxShadow: const [
                                BoxShadow(
                                  color: Colors.black26,
                                  blurRadius: 4.0,
                                ),
                              ],
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                IconButton(
                                  icon: Icon(
                                    isPlaying.value
                                        ? CupertinoIcons.stop
                                        : CupertinoIcons.play_arrow,
                                    color: isPlaying.value
                                        ? QuranColorManager.errorColor
                                        : Colors.black,
                                  ),
                                  onPressed:
                                      isPlaying.value ? stopAudio : startAudio,
                                ),

                                ValueListenableBuilder<bool>(
                                  valueListenable: isBookmarked,
                                  builder: (context, value, child) {
                                    return IconButton(
                                      icon: Icon(
                                        value
                                            ? CupertinoIcons.bookmark_fill
                                            : CupertinoIcons.bookmark,
                                        color: value
                                            ? QuranColorManager.secondaryColor
                                            : Colors.black,
                                      ),
                                      onPressed: () {
                                        if (isBookmarked.value) {
                                          GetStorageService.removeKey(
                                            key: LocalKeys.favoriteVersePage,
                                          );
                                          GetStorageService.removeKey(
                                            key: LocalKeys.favoriteVerse,
                                          );
                                          GetStorageService.removeKey(
                                            key: LocalKeys.favoriteVerseSurah,
                                          );
                                        } else {
                                          GetStorageService.setData(
                                            key: LocalKeys.favoriteVersePage,
                                            value: currentPage,
                                          );

                                          GetStorageService.setData(
                                            key: LocalKeys.favoriteVerse,
                                            value: ayah.id,
                                          );
                                          GetStorageService.setData(
                                            key: LocalKeys.favoriteVerseSurah,
                                            value: ayah.surahNumber,
                                          );
                                        }

                                        isBookmarked.value =
                                            !isBookmarked.value;
                                      },
                                    );
                                  },
                                ),
                                // IconButton(
                                //   icon: const Icon(Icons.share),
                                //   onPressed: share,
                                // ),
                              ],
                            ),
                          ),
                        );
                      }),
                    ),
                  );

                  overlay.insert(currentAyahPopup!);
                }

                return GestureDetector(
                  onLongPressStart: (details) {
                    if (onLongPress != null) {
                      onLongPress!(ayah);
                    } else {
                      // final bookmarkId = bookmarksAyahs.contains(ayah.id)
                      //     ? bookmarks[bookmarksAyahs.indexOf(ayah.id)].id
                      //     : null;
                      // if (bookmarkId != null) {
                      //   AppBloc.bookmarksCubit.removeBookmark(bookmarkId);
                      // } else {
                      // showDialog(
                      //     context: context,
                      //     builder: (ctx) => AyahLongClickDialog(ayah));
                      // }

                      _showPopupMenu(
                        details.globalPosition,
                      );

                      selectedSpan.value = ayah.id.toString();
                    }
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4.0),
                      color: selectedSpan.value == ayah.id.toString()
                          ? QuranColorManager.lightPrimaryColor
                          : GetStorageService.getData(
                                        key: LocalKeys.favoriteVerse,
                                      ) ==
                                      ayah.id ||
                                  (highlightVerseNumber == ayah.ayahNumber &&
                                      highlightVerseSurahNumber ==
                                          ayah.surahNumber)
                              ? QuranColorManager.lightSecondaryColor
                              :
                              // bookmarksAyahs.contains(ayah.id)
                              //         ? Color(bookmarks[bookmarksAyahs.indexOf(ayah.id)]
                              //                 .colorCode)
                              //             .withOpacity(0.7)
                              //         :
                              null,
                    ),
                    child: Text(
                      ayah.ayah,
                      style: FlutterQuran().hafsStyle.copyWith(
                            fontFamily: 'uthman',
                          ),
                      // style: TextStyle(
                      //   fontFamily: 'othmani',
                      // )
                      // FlutterQuran().hafsStyle,
                    ),
                  ),
                );
              }),
            );
          }).toList(),
          style: FlutterQuran().hafsStyle.copyWith(
                fontFamily: 'uthman',
              ),
        )));
  }
}

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_quran/flutter_quran.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';
import 'package:just_audio/just_audio.dart';
import 'package:just_audio_background/just_audio_background.dart';
import 'package:quran/quran.dart';
import 'package:xr_helper/xr_helper.dart';

import 'app_bloc.dart';
import 'controllers/bookmarks_controller.dart';
import 'controllers/quran_controller.dart';
import 'models/quran_constants.dart';
import 'models/quran_page.dart';
import 'utils/quran_color_manager.dart';
import 'widgets/flutter_quran_top_section.widget.dart';

part 'utils/images.dart';
part 'utils/toast_utils.dart';
part 'widgets/ayah_long_click_dialog.dart';
part 'widgets/bsmallah_widget.dart';
part 'widgets/default_drawer.dart';
part 'widgets/quran_line.dart';
part 'widgets/quran_page_bottom_info.dart';
part 'widgets/surah_header_widget.dart';

class FlutterQuranScreen extends HookWidget {
  const FlutterQuranScreen(
      {this.showBottomWidget = true,
      this.useDefaultAppBar = true,
      this.bottomWidget,
      this.appBar,
      this.onPageChanged,
      this.currentPageIndex,
      this.highlightVerseNumber,
      this.highlightVerseSurahNumber,
      this.onPageTap,
      this.page,
      super.key});

  ///[showBottomWidget] is a bool to disable or enable the default bottom widget
  final bool showBottomWidget;

  ///[showBottomWidget] is a bool to disable or enable the default bottom widget
  final bool useDefaultAppBar;

  ///[bottomWidget] if if provided it will replace the default bottom widget
  final Widget? bottomWidget;

  ///[appBar] if if provided it will replace the default app bar
  final PreferredSizeWidget? appBar;

  ///[onPageChanged] if provided it will be called when a quran page changed
  final Function(int)? onPageChanged;

  final ValueNotifier<int?>? currentPageIndex;

  final int? highlightVerseNumber;

  final int? highlightVerseSurahNumber;
  final int? page;
  final Function? onPageTap;

  @override
  Widget build(BuildContext context) {
    final deviceSize = MediaQuery.sizeOf(context);
    Orientation currentOrientation = MediaQuery.of(context).orientation;
    final selectedSpan = useState<String>("");

    final audioPlayer = useState(AudioPlayer());
    final highlightVerseState = useState(highlightVerseNumber);

    return GestureDetector(
      onTap: () {
        selectedSpan.value = "";
        currentAyahPopup?.remove();
        currentAyahPopup = null;
        highlightVerseState.value = null;
        if (onPageTap != null) {
          onPageTap!();
        }
      },
      child: MultiBlocProvider(
        providers: AppBloc.providers,
        child: Directionality(
          textDirection: TextDirection.rtl,
          child: BlocBuilder<QuranCubit, List<QuranPage>>(
            builder: (ctx, pages) {
              return Scaffold(
                // appBar: PreferredSize(
                //   preferredSize: const Size.fromHeight(55),
                //   child: Padding(
                //     padding: const EdgeInsets.only(
                //       top: AppSpaces.padding8,
                //       right: AppSpaces.padding8,
                //       left: AppSpaces.padding8,
                //     ),
                //     child: TopSectionWidget(
                //       currentIndex:
                //           currentPageIndex ?? ValueNotifier<int?>(null),
                //       page: page,
                //       surah: getSurahNameArabic(
                //         pages[(currentPageIndex?.value ?? 1) - 1]
                //             .ayahs
                //             .last
                //             .surahNumber,
                //       ),
                //       hizb: pages[(currentPageIndex?.value ?? 1) - 1].hizb,
                //       // surahName: pages[currentPageIndex?.value??].ayahs.last.surahNameAr)
                //       // pages[(currentPageIndex?.value ?? 0) - 1 ?? 1]
                //       //     .ayahs
                //       //     .last
                //       //     .surahNameAr,
                //     ),
                //   ),
                // ),
                // appBar ??
                //     (useDefaultAppBar
                //         ? AppBar(
                //             elevation: 0,
                //             backgroundColor: Colors.transparent,
                //           )
                //         : null),
                drawer: appBar == null && useDefaultAppBar
                    ? const _DefaultDrawer()
                    : null,
                body: pages.isEmpty
                    ? const Center(child: CircularProgressIndicator())
                    : PageView.builder(
                        itemCount: pages.length,
                        controller: AppBloc.quranCubit.pageController,
                        onPageChanged: (page) {
                          if (onPageChanged != null) onPageChanged!(page);
                          AppBloc.quranCubit.saveLastPage(page + 1);
                        },
                        pageSnapping: true,
                        itemBuilder: (ctx, index) {
                          List<String> newSurahs = [];

                          WidgetsBinding.instance.addPostFrameCallback((_) {
                            currentPageIndex?.value ??= index + 1;
                          });

                          return Stack(
                            alignment: Alignment.bottomCenter,
                            children: [
                              Container(
                                padding: const EdgeInsets.only(
                                  top: 40,
                                  right: 24,
                                  left: 24,
                                ),
                                // margin: const EdgeInsets.only(
                                //   top: 4,
                                //   right: 4,
                                //   left: 4,
                                // ),
                                decoration: const BoxDecoration(
                                  image: DecorationImage(
                                    image: AssetImage(
                                      'assets/images/quran_background.png',
                                      // 'assets/images/quran_background.png',
                                    ),
                                    fit: BoxFit.fill,
                                  ),
                                ),
                                child: index == 0 || index == 1

                                    /// This is for first 2 pages of Quran: Al-Fatihah and Al-Baqarah
                                    ? Center(
                                        child: SingleChildScrollView(
                                          child: Column(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              SurahHeaderWidget(pages[index]
                                                  .ayahs[0]
                                                  .surahNameAr),
                                              if (index == 1)
                                                const BasmallahWidget(),
                                              ...pages[index].lines.map((line) {
                                                return BlocBuilder<
                                                    BookmarksCubit,
                                                    List<Bookmark>>(
                                                  builder:
                                                      (context, bookmarks) {
                                                    final bookmarksAyahs =
                                                        bookmarks
                                                            .map((bookmark) =>
                                                                bookmark.ayahId)
                                                            .toList();
                                                    return Column(
                                                      children: [
                                                        SizedBox(
                                                            width: deviceSize
                                                                    .width -
                                                                32,
                                                            child: QuranLine(
                                                              line,
                                                              bookmarksAyahs,
                                                              bookmarks,
                                                              boxFit: BoxFit
                                                                  .scaleDown,
                                                              selectedSpan:
                                                                  selectedSpan,
                                                              audioPlayer:
                                                                  audioPlayer,
                                                              highlightVerseNumber:
                                                                  highlightVerseState
                                                                      .value,
                                                              highlightVerseSurahNumber:
                                                                  highlightVerseSurahNumber,
                                                              currentPage:
                                                                  index + 1,
                                                            )),
                                                      ],
                                                    );
                                                  },
                                                );
                                              }),
                                            ],
                                          ),
                                        ),
                                      )

                                    /// Other Quran pages
                                    : LayoutBuilder(
                                        builder: (context, constraints) {
                                        return ListView(
                                            physics: currentOrientation ==
                                                    Orientation.portrait
                                                ? const NeverScrollableScrollPhysics()
                                                : null,
                                            children: [
                                              ...pages[index].lines.map((line) {
                                                bool firstAyah = false;
                                                if (line.ayahs[0].ayahNumber ==
                                                        1 &&
                                                    !newSurahs.contains(line
                                                        .ayahs[0]
                                                        .surahNameAr)) {
                                                  newSurahs.add(line
                                                      .ayahs[0].surahNameAr);
                                                  firstAyah = true;
                                                }
                                                return BlocBuilder<
                                                    BookmarksCubit,
                                                    List<Bookmark>>(
                                                  builder:
                                                      (context, bookmarks) {
                                                    final bookmarksAyahs =
                                                        bookmarks
                                                            .map((bookmark) =>
                                                                bookmark.ayahId)
                                                            .toList();
                                                    return Column(
                                                      children: [
                                                        if (firstAyah)
                                                          SurahHeaderWidget(line
                                                              .ayahs[0]
                                                              .surahNameAr),
                                                        if (firstAyah &&
                                                            (line.ayahs[0]
                                                                    .surahNumber !=
                                                                9))
                                                          const BasmallahWidget(),
                                                        SizedBox(
                                                          width:
                                                              deviceSize.width -
                                                                  30,
                                                          height: ((currentOrientation ==
                                                                          Orientation
                                                                              .portrait
                                                                      ? constraints
                                                                          .maxHeight
                                                                      : deviceSize
                                                                          .width) -
                                                                  (pages[index]
                                                                          .numberOfNewSurahs *
                                                                      (line.ayahs[0].surahNumber !=
                                                                              9
                                                                          ? 110
                                                                          : 80))) *
                                                              0.95 /
                                                              pages[index]
                                                                  .lines
                                                                  .length,
                                                          child: QuranLine(
                                                            line,
                                                            bookmarksAyahs,
                                                            bookmarks,
                                                            boxFit: line
                                                                    .ayahs
                                                                    .last
                                                                    .centered
                                                                ? BoxFit
                                                                    .scaleDown
                                                                : BoxFit.fill,
                                                            selectedSpan:
                                                                selectedSpan,
                                                            audioPlayer:
                                                                audioPlayer,
                                                            highlightVerseNumber:
                                                                highlightVerseState
                                                                    .value,
                                                            highlightVerseSurahNumber:
                                                                highlightVerseSurahNumber,
                                                            currentPage:
                                                                index + 1,
                                                          ),
                                                        ),
                                                      ],
                                                    );
                                                  },
                                                );
                                              }),
                                            ]);
                                      }),
                              ),
                              Text((currentPageIndex?.value ?? 1).toString(),
                                  style: const TextStyle(
                                    color: Colors.black87,
                                    fontSize: 14,
                                  )).paddingOnly(bottom: 3, left: 9),
                              Positioned(
                                top: 0,
                                right: 0,
                                left: 0,
                                child: FlutterQuranTopSectionWidget(
                                  currentIndex: currentPageIndex ??
                                      ValueNotifier<int?>(null),
                                  page: page,
                                  surah: getSurahNameArabic(
                                    pages[(currentPageIndex?.value ?? 1) - 1]
                                        .ayahs
                                        .last
                                        .surahNumber,
                                  ),
                                  hizb:
                                      pages[(currentPageIndex?.value ?? 1) - 1]
                                          .hizb,
                                ),
                              ),
                            ],
                          );
                        },
                      ),
              );
            },
          ),
        ),
      ),
    );
  }
}

class _FlutterQuranSearchScreen extends StatefulWidget {
  const _FlutterQuranSearchScreen();

  @override
  State<_FlutterQuranSearchScreen> createState() =>
      _FlutterQuranSearchScreenState();
}

class _FlutterQuranSearchScreenState extends State<_FlutterQuranSearchScreen> {
  List<Ayah> ayahs = [];

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('بحث'),
          centerTitle: true,
        ),
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.max,
              children: [
                TextField(
                  onChanged: (txt) {
                    final searchResult = FlutterQuran().search(txt);
                    setState(() {
                      ayahs = [...searchResult];
                    });
                  },
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.black),
                    ),
                    hintText: 'بحث',
                  ),
                ),
                Expanded(
                  child: ListView(
                    children: ayahs
                        .map((ayah) => Column(
                              children: [
                                ListTile(
                                  title: Text(
                                    ayah.ayah.replaceAll('\n', ' '),
                                  ),
                                  subtitle: Text(ayah.surahNameAr),
                                  contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 16),
                                  onTap: () {
                                    Navigator.of(context).pop();
                                    FlutterQuran().navigateToAyah(ayah);
                                  },
                                ),
                                const Divider(
                                  color: Colors.grey,
                                  thickness: 1,
                                ),
                              ],
                            ))
                        .toList(),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

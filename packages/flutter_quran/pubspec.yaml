name: flutter_quran
description: "A Quran Package you can put in your app and it will work without the need for any other resources."
version: 0.0.2
homepage: https://github.com/heshamerfan97/flutter_quran

environment:
  sdk: '>=2.17.0 <4.0.0'
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter


  # state management
  bloc: ^8.1.4
  flutter_bloc: ^8.1.6
  flutter_hooks: ^0.20.4


  # local storage
  shared_preferences: ^2.3.2

  quran:
    path: ../quran
  just_audio: ^0.9.43
  just_audio_background: ^0.0.1-beta.14

  xr_helper:
    path: ../xr_helper

  #UI
  fluttertoast: ^8.2.8

  #Tools
  intl: ^0.19.0
  share_plus: ^10.1.4
  internet_connection_checker_plus: ^2.7.0

# This package supports all platforms listed below.
# But note that it works and fits on mobile size devices
# Additional Screen Sizes will be supported later
platforms:
  android:
  ios:
  linux:
  macos:
  web:
  windows:

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0


flutter:
  assets:
    - lib/assets/
    - lib/assets/fonts/
    - lib/assets/fonts/hafs.ttf
    - lib/assets/fonts/hafs2.ttf
    - lib/assets/fonts/hafs_uthmanic.ttf
    - lib/assets/jsons/
    - lib/assets/images/
    - lib/assets/images/surah_header.png

  fonts:
    - family: hafs
      fonts:
        - asset: packages/flutter_quran/assets/fonts/hafs.ttf

    - family: hafs2
      fonts:
        - asset: packages/flutter_quran/assets/fonts/hafs2.ttf

    - family: uthman
      fonts:
        - asset: packages/flutter_quran/assets/fonts/hafs_uthmanic.ttf

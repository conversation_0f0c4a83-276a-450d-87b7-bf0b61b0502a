import 'dart:io';

import 'package:background_fetch/background_fetch.dart' hide NetworkType;
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:home_widget/home_widget.dart';
import 'package:quran_broadcast_app/src/core/consts/app_constants.dart';
import 'package:quran_broadcast_app/src/core/shared/services/home_widgets_service/home_widget.service.dart';
import 'package:quran_broadcast_app/src/features/calendar/controllers/calendar.controller.dart';
import 'package:quran_broadcast_app/src/features/calendar/providers/calendar.providers.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../features/home/<USER>/widgets/next_prayer_times.dart';

@pragma('vm:entry-point')
void backgroundFetchHeadlessTask(taskId) async {
  WidgetsFlutterBinding.ensureInitialized();
  await GetStorageService.init();
  await HomeWidget.setAppGroupId(AppConsts.homeWidgetAppGroupId);

  final calendarController =
      ProviderContainer().read(calendarControllerNotifierProvider);

  CalendarController.calendar.value =
      await calendarController.getCalendarFromLocal();

  final now = DateTime.now();
  var currentDayData = calendarController.calendarByDate(now);
  var prayerTimes = currentDayData.prayerTimes;
  var nextPrayerTime = getNextPrayerTime(prayerTimes, date: now);

  if (nextPrayerTime.name == AppConsts.prayerNames[0]) {
    final nextDay = (now.hour >= 17 && now.hour <= 23)
        ? DateTime(now.year, now.month, now.day + 1, 0, 0)
        : now;

    // Reduced logging for background tasks to minimize storage usage
    currentDayData = calendarController.calendarByDate(nextDay);

    prayerTimes = currentDayData.prayerTimes;

    nextPrayerTime = getNextPrayerTime(prayerTimes, date: nextDay);
  }

  currentDayData = calendarController.calendarByDate(now);
  prayerTimes = currentDayData.prayerTimes;

  Log.w(
      'Next prayer time: ${nextPrayerTime.name}, ${nextPrayerTime.time}, Date: $now\nDone');

  await HomeWidgetService.savePrayerTimes(prayerTimes, nextPrayerTime);
  await HomeWidgetService
      .saveIOSAllPrayerTimesForWidget(); // Save all 30 days of data
  await HomeWidget.updateWidget(
      iOSName: AppConsts.iosWidget, androidName: AppConsts.androidWidget);

  if (Platform.isAndroid) {
    await HomeWidget.updateWidget(
        iOSName: AppConsts.iosWidget, androidName: AppConsts.androidWidget4x1);
  }

  try {
    // For Android, check if we need to schedule next day's prayer times
    if (Platform.isAndroid) {
      final now = DateTime.now();

      // If this is the last prayer of the day (Isha) or it's past 11 PM, schedule tomorrow's prayers
      if (nextPrayerTime.name == AppConsts.prayerNames[0] || now.hour >= 23) {
        Log.i('Scheduling next day prayer times...');
        await scheduleAllDailyPrayerUpdates();
      }
    }

    BackgroundFetch.finish(taskId.toString());
  } catch (e) {
    Log.e('Error in background task completion: $e');
    BackgroundFetch.finish(taskId.toString());
  }
}

void scheduleNextUpdate(DateTime nextPrayerTime) async {
  if (Platform.isIOS) {
    // For iOS, we rely on the widget's own timeline updates
    Log.i('iOS: Widget will handle its own timeline updates');
    return;
  }

  // For Android, schedule all prayer times for today and tomorrow
  await scheduleAllDailyPrayerUpdates();
}

Future<void> scheduleAllDailyPrayerUpdates() async {
  if (Platform.isIOS) return;

  try {
    final calendarController =
        ProviderContainer().read(calendarControllerNotifierProvider);

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));

    // Clear existing scheduled tasks
    await BackgroundFetch.stop();

    // Schedule prayer times for today (if any remaining)
    await _schedulePrayerTimesForDate(calendarController, today, 'today');

    // Schedule prayer times for tomorrow
    await _schedulePrayerTimesForDate(calendarController, tomorrow, 'tomorrow');

    // Schedule midnight update for the day after tomorrow
    final dayAfterTomorrow = tomorrow.add(const Duration(days: 1));
    final midnightUpdate = DateTime(dayAfterTomorrow.year,
        dayAfterTomorrow.month, dayAfterTomorrow.day, 0, 0);
    await _scheduleTask(
        'midnight_${dayAfterTomorrow.millisecondsSinceEpoch}', midnightUpdate);

    Log.i('Android: Scheduled all prayer time updates for today and tomorrow');
  } catch (e) {
    Log.e('Error scheduling daily prayer updates: $e');
  }
}

Future<void> _schedulePrayerTimesForDate(CalendarController calendarController,
    DateTime date, String dayLabel) async {
  try {
    final dayData = calendarController.calendarByDate(date);
    final prayerTimes = dayData.prayerTimes;
    final now = DateTime.now();

    // List of prayer times to schedule
    final prayers = [
      {'name': 'fajr', 'time': prayerTimes.fajr},
      {'name': 'sunrise', 'time': prayerTimes.sunrise},
      {'name': 'dhuhr', 'time': prayerTimes.dhuhr},
      {'name': 'asr', 'time': prayerTimes.asr},
      {'name': 'maghrib', 'time': prayerTimes.maghrib},
      {'name': 'isha', 'time': prayerTimes.isha},
    ];

    for (final prayer in prayers) {
      try {
        final timeParts = prayer['time']!.split(':');
        final hour = int.parse(timeParts[0]);
        final minute = int.parse(timeParts[1]);
        final prayerDateTime =
            DateTime(date.year, date.month, date.day, hour, minute);

        // Only schedule if the prayer time is in the future
        if (prayerDateTime.isAfter(now)) {
          final taskId = '${prayer['name']}_${date.millisecondsSinceEpoch}';
          await _scheduleTask(taskId, prayerDateTime);
          Log.i(
              'Scheduled ${prayer['name']} for $dayLabel at ${prayer['time']}');
        }
      } catch (e) {
        Log.e(
            'Error parsing prayer time ${prayer['name']}: ${prayer['time']} - $e');
      }
    }
  } catch (e) {
    Log.e('Error scheduling prayer times for $dayLabel: $e');
  }
}

Future<void> _scheduleTask(String taskId, DateTime scheduledTime) async {
  final now = DateTime.now();
  final delay = scheduledTime.difference(now).inMilliseconds;

  if (delay > 0) {
    await BackgroundFetch.scheduleTask(
      TaskConfig(
        taskId: taskId,
        delay: delay,
        periodic: false,
        forceAlarmManager: true,
        stopOnTerminate: false,
        startOnBoot: true,
        requiresBatteryNotLow: false,
        requiresCharging: false,
        requiresDeviceIdle: false,
        requiresStorageNotLow: false,
        enableHeadless: true,
      ),
    );
    Log.i('Scheduled task $taskId for ${scheduledTime.toString()}');
  }
}

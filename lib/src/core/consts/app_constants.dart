import 'dart:io';

import 'package:flutter/material.dart'
    show BoxShadow, Color, Locale, LocalizationsDelegate, Offset;
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:quran_broadcast_app/generated/l10n.dart';
import 'package:xr_helper/xr_helper.dart';

class AppConsts {
  static const String appName = 'Quran Broadcast App';
  static const Locale locale = Locale('ar');
  static const fontFamily = 'Alex';
  static final String homeWidgetAppGroupId =
      Platform.isIOS ? 'group.qurankareem' : 'group.com.perfectfit.qurankareem';
  static const String iosWidget = 'PrayerWidget';
  static const String androidWidget = 'AndroidAzanWidget';
  static const String androidWidget4x1 = 'AndroidAzanWidget4x1';
  static const String liveUrl = 'http://www.quran-radio.org:8002';

  static const List<Locale> supportedLocales = [
    Locale('ar'),
    locale,
  ];

  static bool get isEnglish =>
      GetStorageService.getData(key: LocalKeys.language) == 'en';

  static const List<LocalizationsDelegate> localizationsDelegates = [
    S.delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  //? Test Login
  static const String testEmail = 'admin';
  static const String testPass = 'test@123';

  //? Notification Channels
  static const String azanChannelKey = 'azan_channel';
  static const String reminderChannelKey = 'reminder_channel';
  static const String shroukChannelKey = 'shrouk_channel';

  static final prayerNames = [
    "الفجر",
    "الشروق",
    "الظهر",
    "العصر",
    "المغرب",
    "العشاء",
  ];

  // static final prayerTimes = [
  //   "12:30",
  //   "12:50",
  //   "13:10",
  //   "13:30",
  //   "13:50",
  //   "14:10",
  // ];

  // [
  //   "04:50 ص",
  //   "05:50 ص",
  //   "01:45 م",
  //   "04:15 م",
  //   "06:12 م",
  //   "07:30 م",
  // ];

  static final prayerTimeReminders = [
    "قبل 5 دقائق",
    "قبل 10 دقائق",
    "قبل 15 دقيقة",
    "قبل 20 دقيقة",
  ];

  // static final staticPrayerTimeReminders = [
  //   "قبل 5 دقائق",
  //   "قبل 10 دقائق",
  //   "قبل 15 دقائق",
  //   "قبل 20 دقائق",
  // ];

  static const baseBoxShadow = [
    BoxShadow(
      color: Color(0x00000014),
      blurRadius: 20,
      spreadRadius: 3,
      offset: Offset(0, 3),
    )
  ];
}

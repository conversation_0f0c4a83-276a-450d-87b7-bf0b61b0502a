import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_quran/flutter_quran.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:quran/quran.dart';
import 'package:quran_broadcast_app/src/core/theme/color_manager.dart';
import 'package:quran_broadcast_app/src/features/main_screen/view/main.screen.dart';
import 'package:quran_broadcast_app/src/features/quran/models/search_by_type.dart';
import 'package:quran_broadcast_app/src/features/quran/view/quran_screen/quran.screen.dart';
import 'package:quran_broadcast_app/src/features/quran/view/quran_screen/widgets/search_widgets/search_section.widget.dart';
import 'package:xr_helper/xr_helper.dart';

//
class TopSectionWidget extends HookWidget {
  final int index;

  const TopSectionWidget({
    super.key,
    required this.index,
  });

  @override
  Widget build(BuildContext context) {
    final isFavorite = useBookmark(index);

    void showSearchDialog(BuildContext context) {
      showDialog(
        context: context,
        barrierDismissible: true,
        builder: (BuildContext context) {
          return HookBuilder(builder: (context) {
            final searchValue = useState<String>('');
            final pageNumbers = useState<List<num>>([]);
            final ayaFiltered = useState<Map?>(null);
            final selectedSearchBy = useState(SearchByFieldType.surah);

            return GestureDetector(
              onTap: () {
                context.back();
              },
              child: Material(
                color: Colors.black54,
                child: GestureDetector(
                  onTap: () {},
                  child: Center(
                    child: Container(
                      margin: const EdgeInsets.symmetric(
                          horizontal: AppSpaces.padding24,
                          vertical: AppSpaces.padding64),
                      decoration: BoxDecoration(
                        color: ColorManager.white,
                        borderRadius: BorderRadius.circular(AppRadius.radius12),
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(
                              right: AppSpaces.padding8,
                              left: AppSpaces.padding8,
                              bottom: AppSpaces.padding8,
                              top: AppSpaces.padding24,
                            ),
                            child: QuranSearchSection(
                              searchValue: searchValue,
                              pageNumbers: pageNumbers,
                              selectedSearchBy: selectedSearchBy,
                              ayaFiltered: ayaFiltered,
                            ),
                          ),
                          Expanded(
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: AppSpaces.padding4),
                              child: GetSelectedSurahListView(
                                searchValue: searchValue,
                                selectedSearchBy: selectedSearchBy,
                                pageNumbers: pageNumbers,
                                ayaFiltered: ayaFiltered,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            );
          });
        },
      );
    }

    String getHizbSection(int pageNumber) {
      List<int> hizbPages = hizbByPageNumber.keys.map(int.parse).toList();
      hizbPages.sort(); // Ensure pages are sorted

      int? hizbStartPage;
      int? hizbNumber;

      // Find the closest hizb start page ≤ given page
      for (var page in hizbPages) {
        if (page <= pageNumber) {
          hizbStartPage = page;
          hizbNumber = hizbByPageNumber[page.toString()];
        } else {
          break;
        }
      }

      if (hizbStartPage == null || hizbNumber == null) {
        return "غير معروف"; // Not found
      }

      int position = pageNumber - hizbStartPage;

      if (position == 0) {
        return "الحزب $hizbNumber";
      } else if (position < 3) {
        return "ربع الحزب $hizbNumber";
      } else if (position < 6) {
        return "نصف الحزب $hizbNumber";
      } else if (position < 8) {
        return "ثلاث أرباع الحزب $hizbNumber";
      } else {
        return "الحزب ${hizbNumber + 1}"; // Next Hizb
      }
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            InkWell(
              radius: AppRadius.radius100,
              borderRadius: BorderRadius.circular(AppRadius.radius100),
              onTap: () {
                const MainScreen().navigateReplacement;
              },
              child: const Icon(
                CupertinoIcons.back,
              ),
            ),
            AppGaps.gap8,
            Row(
              children: [
                const Icon(
                  CupertinoIcons.book,
                  color: ColorManager.primaryColor,
                  size: 30,
                ),
                const SizedBox(width: 8),
                Text(
                  suraJsonData.value[getPageData(index)[0]["surah"] - 1]
                      ["name"],
                  style: AppTextStyles.subTitle.copyWith(
                    fontFamily: GoogleFonts.amiri().fontFamily,
                    letterSpacing: 0.2,
                  ),
                ),
              ],
            ),
          ],
        ),
        Text(
          "الجزء ${NumberFormat('#.##', 'ar_EG').format(getJuzNumber(getPageData(index)[0]["surah"], getPageData(index)[0]["start"]))}",
          style: AppTextStyles.labelLarge.copyWith(
            fontSize: 13,
            color: Colors.black.withOpacity(0.6),
          ),
        ),
        if (hizbByPageNumber[index.toString()] != null)
          Text(
            // getHizbSection(index),
            "الحزب ${hizbByPageNumber[index.toString()]}",
            style: AppTextStyles.labelLarge.copyWith(
              fontSize: 13,
              color: Colors.black.withOpacity(0.6),
            ),
          ),
        Row(
          children: [
            CircleAvatar(
              backgroundColor: isFavorite.value
                  ? ColorManager.primaryColor
                  : ColorManager.secondaryColor,
              radius: 20,
              child: IconButton(
                icon: Icon(
                  isFavorite.value
                      ? CupertinoIcons.bookmark_fill
                      : CupertinoIcons.bookmark,
                  color: ColorManager.white,
                  size: 18,
                ),
                onPressed: () {
                  if (isFavorite.value == true) {
                    isFavorite.value = false;
                    GetStorageService.removeKey(
                      key: LocalKeys.favoritePage,
                    );
                  } else {
                    isFavorite.value = true;
                    GetStorageService.setData(
                      key: LocalKeys.favoritePage,
                      value: index,
                    );
                  }
                },
              ),
            ),
            AppGaps.gap8,
            CircleAvatar(
              backgroundColor: ColorManager.primaryColor,
              radius: 20,
              child: IconButton(
                icon: const Icon(
                  CupertinoIcons.search,
                  color: ColorManager.white,
                  size: 18,
                ),
                onPressed: () {
                  showSearchDialog(context);
                },
              ),
            ),
          ],
        ),
      ],
    );
  }
}

// class TopSectionWidget extends HookWidget {
//   final int index;
//
//   const TopSectionWidget({
//     super.key,
//     required this.index,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     final isFavorite = useBookmark(index);
//
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//       children: [
//         Row(
//           children: [
//             IconButton(
//               onPressed: () {
//                 context.back();
//               },
//               icon: const Icon(
//                 CupertinoIcons.back,
//               ),
//             ),
//             // AppGaps.gap4,
//             Row(
//               children: [
//                 const Icon(
//                   CupertinoIcons.book,
//                   color: ColorManager.primaryColor,
//                   size: 30,
//                 ),
//                 const SizedBox(width: 8),
//                 Text(
//                   suraJsonData.value[getPageData(index)[0]["surah"] - 1]
//                       ["name"],
//                   style: AppTextStyles.subTitle.copyWith(
//                     fontFamily: GoogleFonts.amiri().fontFamily,
//                     letterSpacing: 0.2,
//                   ),
//                 ),
//               ],
//             ),
//           ],
//         ),
//         Row(
//           children: [
//             Text(
//               "الجزء ${getJuzNumber(getPageData(index)[0]["surah"], getPageData(index)[0]["start"])}",
//               style: AppTextStyles.labelLarge.copyWith(
//                 fontSize: 13,
//                 color: Colors.black.withOpacity(0.6),
//               ),
//             ),
//             AppGaps.gap8,
//             CircleAvatar(
//               backgroundColor: isFavorite.value
//                   ? ColorManager.primaryColor
//                   : ColorManager.secondaryColor,
//               radius: 20,
//               child: IconButton(
//                 icon: Icon(
//                   isFavorite.value
//                       ? CupertinoIcons.bookmark_fill
//                       : CupertinoIcons.bookmark,
//                   color: ColorManager.white,
//                   size: 18,
//                 ),
//                 onPressed: () {
//                   if (isFavorite.value == true) {
//                     isFavorite.value = false;
//                     GetStorageService.removeKey(
//                       key: LocalKeys.favoritePage,
//                     );
//                   } else {
//                     isFavorite.value = true;
//                     GetStorageService.setData(
//                       key: LocalKeys.favoritePage,
//                       value: index,
//                     );
//                   }
//                 },
//               ),
//             ),
//           ],
//         ),
//       ],
//     );
//   }
// }

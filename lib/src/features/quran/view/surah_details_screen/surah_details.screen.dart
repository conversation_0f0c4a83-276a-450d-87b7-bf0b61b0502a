import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_quran/flutter_quran.dart';
import 'package:just_audio/just_audio.dart';
import 'package:quran/quran.dart';
import 'package:quran_broadcast_app/generated/assets.gen.dart';
import 'package:quran_broadcast_app/src/features/main_screen/view/main.screen.dart';
import 'package:quran_broadcast_app/src/features/quran/view/surah_details_screen/widgets/surah_details.widget.dart';
import 'package:wakelock_plus/wakelock_plus.dart';
import 'package:xr_helper/xr_helper.dart';

OverlayEntry? currentAyahPopup;

class SurahDetailsScreen extends HookWidget {
  final num pageNumber;
  final String highlightVerse;

  const SurahDetailsScreen({
    super.key,
    required this.pageNumber,
    required this.highlightVerse,
  });

  @override
  Widget build(BuildContext context) {
    final index = useState(pageNumber.toInt());
    final selectedSpan = useState("");
    final audioPlayer = useState(AudioPlayer());
    final isPageFavourite = useBookmark(pageNumber.toInt());
    final pageController = usePageController(initialPage: index.value);
    final highlightVerseState = useState(highlightVerse);
    final richTextKeys = List.generate(604, (_) => GlobalKey());
    final isFullScreen = useState(true);

    useEffect(() {
      WakelockPlus.enable();

      return () {
        WakelockPlus.disable();
      };
    }, []);

    void clearData() {
      isPageFavourite.value = false;
      highlightVerseState.value = "";
      selectedSpan.value = "";
      currentAyahPopup?.remove();
      currentAyahPopup = null;
      audioPlayer.value.stop();
    }

    void toggleFullScreen() {
      if (currentAyahPopup != null || selectedSpan.value.isNotEmpty) {
        clearData();
      }
    }

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        clearData();
        const MainScreen().navigateReplacement;
      },
      child: GestureDetector(
        onTap: () {
          toggleFullScreen();
        },
        child: Scaffold(
          body: Center(
            child: PageView.builder(
              reverse: false,
              scrollDirection: Axis.horizontal,
              physics: const PageScrollPhysics()
                  .applyTo(const BouncingScrollPhysics()),
              onPageChanged: (a) {
                clearData();
                index.value = a;
              },
              controller: pageController,
              itemCount: totalPagesCount + 1,
              itemBuilder: (context, index) {
                if (index == 0) {
                  return Assets.images.quranCover.image();
                }

                return SurahDetailsWidget(
                  index: index,
                  highlightVerse: highlightVerseState.value,
                  selectedSpan: selectedSpan,
                  richTextKeys: richTextKeys,
                  audioPlayer: audioPlayer,
                  isFullScreen: isFullScreen.value,
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}

// class SurahDetailsScreen extends HookWidget {
//   final num pageNumber;
//   final String highlightVerse;
//
//   const SurahDetailsScreen({
//     super.key,
//     required this.pageNumber,
//     required this.highlightVerse,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     final index = useState(pageNumber.toInt());
//     final selectedSpan = useState("");
//     final audioPlayer = useState(AudioPlayer());
//     final isPageFavourite = useBookmark(pageNumber.toInt());
//     final pageController = usePageController(initialPage: index.value);
//     final highlightVerseState = useState(highlightVerse);
//     final richTextKeys = List.generate(604, (_) => GlobalKey());
//     final isFullScreen = useState(true);
//
//     useEffect(() {
//       SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
//       SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
//       WakelockPlus.enable();
//
//       return () {
//         SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
//         WakelockPlus.disable();
//       };
//     }, []);
//
//     void clearData() {
//       isPageFavourite.value = false;
//       highlightVerseState.value = "";
//       selectedSpan.value = "";
//       currentAyahPopup?.remove();
//       currentAyahPopup = null;
//     }
//
//     void toggleFullScreen() {
//       if (currentAyahPopup != null || selectedSpan.value.isNotEmpty) {
//         clearData();
//         return;
//       }
//
//       isFullScreen.value = !isFullScreen.value;
//       if (isFullScreen.value) {
//         SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
//       } else {
//         SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
//         WakelockPlus.disable();
//       }
//     }
//
//     return PopScope(
//       canPop: true,
//       onPopInvokedWithResult: (didPop, result) {
//         clearData();
//       },
//       child: GestureDetector(
//         onTap: () {
//           toggleFullScreen();
//         },
//         child: Scaffold(
//           body: PageView.builder(
//             reverse: false,
//             scrollDirection: Axis.horizontal,
//             onPageChanged: (a) {
//               clearData();
//               index.value = a;
//             },
//             controller: pageController,
//             itemCount: totalPagesCount + 1,
//             itemBuilder: (context, index) {
//               if (index == 0) {
//                 return Assets.images.quranCover.image();
//               }
//
//               return SurahDetailsWidget(
//                 index: index,
//                 highlightVerse: highlightVerseState.value,
//                 selectedSpan: selectedSpan,
//                 richTextKeys: richTextKeys,
//                 audioPlayer: audioPlayer,
//                 isFullScreen: isFullScreen.value,
//               );
//             },
//           ),
//         ),
//       ),
//     );
//   }
// }
// class SurahDetailsScreen extends HookWidget {
//   final num pageNumber;
//   final String highlightVerse;
//
//   const SurahDetailsScreen({
//     super.key,
//     required this.pageNumber,
//     required this.highlightVerse,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     final index = useState(pageNumber.toInt());
//     final selectedSpan = useState("");
//     final audioPlayer = useState(AudioPlayer());
//
//     final isPageFavourite = useBookmark(pageNumber.toInt());
//
//     final pageController = usePageController(initialPage: index.value);
//     final highlightVerseState = useState(highlightVerse);
//     final richTextKeys = List.generate(604, (_) => GlobalKey());
//
//     final isFullScreen = useState(true);
//
//     useEffect(() {
//       SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
//       SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
//       WakelockPlus.enable();
//
//       return () {
//         SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
//         WakelockPlus.disable();
//       };
//     }, []);
//
//     void clearData() {
//       isPageFavourite.value = false;
//       highlightVerseState.value = "";
//       selectedSpan.value = "";
//       currentAyahPopup?.remove();
//       currentAyahPopup = null;
//     }
//
//     void toggleFullScreen() {
//       if (currentAyahPopup != null || selectedSpan.value.isNotEmpty) {
//         clearData();
//         return;
//       }
//
//       isFullScreen.value = !isFullScreen.value;
//       if (isFullScreen.value) {
//         SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
//       } else {
//         SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
//         WakelockPlus.disable();
//       }
//     }
//
//     return PopScope(
//       canPop: true,
//       onPopInvokedWithResult: (didPop, result) {
//         if (didPop) {
//           clearData();
//         }
//       },
//       child: GestureDetector(
//         onTap: () {
//           toggleFullScreen();
//         },
//         child: Scaffold(
//           body: PageView.builder(
//             reverse: false,
//             scrollDirection: Axis.horizontal,
//             onPageChanged: (a) {
//               clearData();
//
//               index.value = a;
//             },
//             controller: pageController,
//             itemCount: totalPagesCount + 1,
//             itemBuilder: (context, index) {
//               if (index == 0) {
//                 return Assets.images.quranCover.image();
//               }
//
//               return SurahDetailsWidget(
//                 index: index,
//                 highlightVerse: highlightVerseState.value,
//                 selectedSpan: selectedSpan,
//                 richTextKeys: richTextKeys,
//                 audioPlayer: audioPlayer,
//                 isFullScreen: isFullScreen.value,
//               );
//             },
//           ),
//         ),
//       ),
//     );
//   }
// }

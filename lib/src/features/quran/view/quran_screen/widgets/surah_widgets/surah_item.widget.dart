import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_quran/flutter_quran.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:quran_broadcast_app/src/core/theme/color_manager.dart';
import 'package:quran_broadcast_app/src/features/quran/models/search_by_type.dart';
import 'package:quran_broadcast_app/src/features/quran/models/surah_model.dart';
import 'package:quran_broadcast_app/src/features/quran/view/surah_details_screen/surah_details.screen.dart';
import 'package:xr_helper/xr_helper.dart';

class SurahItemWidget extends HookWidget {
  final SurahModel surah;
  final num pageNumber;
  final SearchByFieldType selectedSearchBy;

  const SurahItemWidget({
    super.key,
    required this.surah,
    required this.pageNumber,
    this.selectedSearchBy = SearchByFieldType.surah,
  });

  @override
  Widget build(BuildContext context) {
    final revelationTypeText =
        surah.revelationType == 'Meccan' ? 'مكية' : 'مدنية';

    final ayaText =
        surah.ayahCount == 1 || (surah.ayahCount ?? 0) > 10 ? 'آية' : 'آيات';

    final searchJuzOrHizbOrPageOrAyaText = selectedSearchBy ==
                SearchByFieldType.surah ||
            selectedSearchBy == SearchByFieldType.verse
        ? '$revelationTypeText - ${NumberFormat('#.##', 'ar_EG').format(surah.ayahCount)} $ayaText'
        : selectedSearchBy == SearchByFieldType.juz
            ? 'الجزء ${NumberFormat('#.##', 'ar_EG').format(surah.juz)}'
            : selectedSearchBy == SearchByFieldType.hizb
                ? 'الحزب ${NumberFormat('#.##', 'ar_EG').format(surah.hizb)}'
                : selectedSearchBy == SearchByFieldType.page
                    ? 'الصفحة ${NumberFormat('#.##', 'ar_EG').format(pageNumber)}'
                    : '';

    final isBookmark = useBookmark(pageNumber);

    return Container(
      padding: const EdgeInsets.all(AppSpaces.padding4),
      decoration: BoxDecoration(
        color: ColorManager.white,
        borderRadius: BorderRadius.circular(AppRadius.radius12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.5),
            spreadRadius: 1,
            blurRadius: 7,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: ColorManager.lightPrimaryColor,
          child: Text(
            NumberFormat('#.##', 'ar_EG').format(surah.suraNumber),
            style: const TextStyle(
              color: ColorManager.primaryColor,
              fontWeight: FontWeight.bold,
              fontFamily: 'Alex',
            ),
          ),
        ),
        title: FittedBox(
          fit: BoxFit.scaleDown,
          alignment: Alignment.centerRight,
          child: Text(
            surah.suraArName,
            style: AppTextStyles.subTitle.copyWith(
              fontFamily: GoogleFonts.amiri().fontFamily,
              letterSpacing: 0.2,
            ),
          ),
        ),
        subtitle: FittedBox(
          fit: BoxFit.scaleDown,
          alignment: Alignment.centerRight,
          child: Text(surah.suraEnName,
              style: const TextStyle(
                fontFamily: 'Alex',
              )),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              searchJuzOrHizbOrPageOrAyaText,
              style: const TextStyle(
                fontFamily: 'Alex',
              ),
            ),
            AppGaps.gap12,
            CircleAvatar(
              backgroundColor: isBookmark.value
                  ? ColorManager.primaryColor
                  : ColorManager.secondaryColor,
              radius: 17,
              child: IconButton(
                icon: Icon(
                  isBookmark.value
                      ? CupertinoIcons.bookmark_fill
                      : CupertinoIcons.bookmark,
                  color: ColorManager.white,
                  size: 18,
                ),
                onPressed: () {
                  if (isBookmark.value) {
                    GetStorageService.removeKey(key: LocalKeys.favoritePage);
                  } else {
                    GetStorageService.setData(
                        key: LocalKeys.favoritePage, value: pageNumber);
                  }

                  isBookmark.value = !isBookmark.value;
                },
              ),
            )
          ],
        ),
        onTap: () {
          SurahDetailsScreen(highlightVerse: "", pageNumber: pageNumber)
              .navigate;
        },
      ),
    );
  }
}
